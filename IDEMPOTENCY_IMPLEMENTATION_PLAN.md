# Plan Implementasi Idempotency untuk ATMA Backend

## Overview

Dokumen ini menjelaskan implementasi idempotency untuk mencegah duplikasi efek dari request yang sama di sistem ATMA (AI-Driven Talent Mapping Assessment).

## Masalah yang Diselesaikan

1. **Assessment Service**: Client submit assessment yang sama berkali-kali
2. **Analysis Worker**: Job diproses berkali-kali karena retry mechanism
3. **Archive Service**: Hasil analisis disimpan berkali-kali

## Strategi Implementasi

### Phase 1: Database-Level Protection (Priority: Critical)
### Phase 2: Application-Level Deduplication (Priority: Important)

---

## 1. Assessment Service Idempotency

### Tujuan
Mencegah submission assessment yang sama dari user yang sama dalam periode waktu tertentu.

### Strategi
- Content-based deduplication menggunakan hash dari assessment data
- Time-based deduplication untuk mencegah rapid submissions
- Integration dengan Archive Service untuk check existing jobs

### File yang Terpengaruh

#### 1.1 `assessment-service/src/middleware/idempotency.js` (NEW)
**Tujuan**: Middleware untuk check duplicate submissions
**Implementasi**:
```javascript
const crypto = require('crypto');

// Create hash from assessment data
function createAssessmentHash(userId, assessmentData) {
  const content = JSON.stringify({
    userId,
    riasec: assessmentData.riasec,
    ocean: assessmentData.ocean,
    viaIs: assessmentData.viaIs,
    assessmentName: assessmentData.assessmentName || 'AI-Driven Talent Mapping'
  });
  return crypto.createHash('sha256').update(content).digest('hex');
}

// Check for duplicate submissions
async function checkDuplicateSubmission(req, res, next) {
  // Implementation details...
}
```

#### 1.2 `assessment-service/src/services/deduplicationService.js` (NEW)
**Tujuan**: Service untuk handle deduplication logic
**Implementasi**:
- Query Archive Service untuk existing jobs
- Check time-based constraints
- Return existing jobId/resultId jika duplicate

#### 1.3 `assessment-service/src/routes/assessments.js` (MODIFY)
**Bagian yang diubah**: POST `/assessments/submit` endpoint
**Perubahan**:
- Tambah middleware `checkDuplicateSubmission` sebelum processing
- Handle response untuk duplicate submissions
- Add assessment hash ke job creation

### Yang Harus Diperhatikan
- Performance impact dari additional queries
- Backward compatibility dengan existing clients
- Error handling jika Archive Service tidak available

---

## 2. Archive Service Idempotency

### Tujuan
Mencegah duplikasi data di database dan ensure atomic operations.

### Strategi
- Database constraints untuk prevent duplicate records
- Upsert operations untuk handle conflicts gracefully
- Job status validation untuk ensure valid state transitions

### File yang Terpengaruh

#### 2.1 Database Migration (NEW)
**File**: `archive-service/migrations/add_idempotency_constraints.sql`
**Tujuan**: Tambah database constraints untuk idempotency
**Implementasi**:
```sql
-- Add assessment_hash column to analysis_jobs
ALTER TABLE archive.analysis_jobs 
ADD COLUMN assessment_hash VARCHAR(64);

-- Add unique constraint for active jobs per user+hash
CREATE UNIQUE INDEX idx_analysis_jobs_user_hash_active 
ON archive.analysis_jobs (user_id, assessment_hash) 
WHERE status IN ('queued', 'processing');

-- Add constraint for result deduplication
ALTER TABLE archive.analysis_results
ADD CONSTRAINT unique_user_job_result 
UNIQUE (user_id, assessment_data);
```

#### 2.2 `archive-service/src/models/AnalysisJob.js` (MODIFY)
**Bagian yang diubah**: Model definition
**Perubahan**:
- Tambah field `assessment_hash`
- Tambah validation untuk status transitions
- Add instance methods untuk atomic updates

#### 2.3 `archive-service/src/routes/jobs.js` (MODIFY)
**Bagian yang diubah**: POST `/archive/jobs` endpoint
**Perubahan**:
- Handle duplicate job creation dengan graceful response
- Implement upsert logic
- Add assessment hash calculation

#### 2.4 `archive-service/src/routes/results.js` (MODIFY)
**Bagian yang diubah**: POST `/archive/results` endpoint
**Perubahan**:
- Handle duplicate result creation
- Return existing result jika sudah ada
- Implement conflict resolution

### Yang Harus Diperhatikan
- Database migration harus backward compatible
- Handle existing data yang belum punya assessment_hash
- Performance impact dari additional indexes

---

## 3. Analysis Worker Idempotency

### Tujuan
Mencegah job diproses berkali-kali oleh multiple workers atau retry mechanism.

### Strategi
- Atomic job claiming sebelum processing
- Job status checking sebelum mulai processing
- Result deduplication sebelum save

### File yang Terpengaruh

#### 3.1 `analysis-worker/src/processors/assessmentProcessor.js` (MODIFY)
**Bagian yang diubah**: Main processing function
**Perubahan**:
- Tambah job claiming logic di awal processing
- Check job status sebelum processing
- Handle job already processed scenario

#### 3.2 `analysis-worker/src/services/archiveService.js` (MODIFY)
**Bagian yang diubah**: `saveAnalysisResult` function
**Perubahan**:
- Check existing result sebelum save
- Handle duplicate save gracefully
- Return existing result jika sudah ada

#### 3.3 `analysis-worker/src/services/jobClaimingService.js` (NEW)
**Tujuan**: Service untuk atomic job claiming
**Implementasi**:
```javascript
const claimJob = async (jobId, workerId) => {
  // Atomic update job status to 'processing'
  // Include worker_id and processing_started_at
  // Throw error if job already claimed
};
```

### Yang Harus Diperhatikan
- Race condition handling antara multiple workers
- Timeout mechanism untuk stuck jobs
- Worker crash recovery

---


## Configuration

### Environment Variables
```bash
# Idempotency feature flags
IDEMPOTENCY_ENABLED=true
DEDUPLICATION_WINDOW_HOURS=24

# Worker identification
WORKER_ID=worker-001
```

### Database Queries untuk Monitoring
```sql
-- Check duplicate job attempts
SELECT assessment_hash, COUNT(*) as attempts
FROM archive.analysis_jobs
WHERE created_at > NOW() - INTERVAL '1 day'
GROUP BY assessment_hash
HAVING COUNT(*) > 1;

-- Check job processing efficiency
SELECT status, COUNT(*) as count,
       AVG(EXTRACT(EPOCH FROM (completed_at - created_at))) as avg_processing_time
FROM archive.analysis_jobs
WHERE created_at > NOW() - INTERVAL '1 day'
GROUP BY status;
```

## Implementation Timeline

### Week 1: Database-Level Protection
- [ ] Create database migrations
- [ ] Implement Archive Service constraints
- [ ] Test constraint behavior

### Week 2: Assessment Service Deduplication
- [ ] Implement deduplication middleware
- [ ] Create deduplication service
- [ ] Integration testing

### Week 3: Analysis Worker Protection
- [ ] Implement job claiming service
- [ ] Modify assessment processor
- [ ] Test worker coordination

### Week 4: Testing & Monitoring
- [ ] End-to-end testing
- [ ] Add metrics and monitoring
- [ ] Performance optimization
- [ ] Documentation update

## Testing Strategy

### Unit Tests
- Deduplication logic untuk setiap service
- Hash generation consistency
- Database constraint behavior

### Integration Tests
- End-to-end duplicate submission scenarios
- Worker coordination testing
- Database constraint behavior

### Load Tests
- Performance impact measurement
- Concurrent submission handling
- System behavior under high load

## Rollback Plan

Setiap phase memiliki rollback mechanism:
1. **Database constraints**: Dapat di-drop tanpa data loss
2. **Application logic**: Feature flags untuk disable
3. **No external dependencies**: Semua menggunakan existing infrastructure

## Success Metrics

- **Duplicate submission rate**: < 1% dari total submissions
- **Processing efficiency**: No performance degradation > 5%
- **Data integrity**: 100% prevention dari duplicate results
- **System reliability**: No data corruption dari race conditions

## Simplified Architecture Benefits

✅ **No Redis dependency** - Menggunakan PostgreSQL untuk semua persistence
✅ **Reduced complexity** - Fokus pada 3 area critical saja
✅ **Lower maintenance** - Tidak ada additional infrastructure
✅ **Better reliability** - Fewer moving parts, fewer failure points
